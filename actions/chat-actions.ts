'use server';

import { auth } from "@/auth";
import ollama from 'ollama';
import { revalidatePath } from "next/cache";
import prisma from "@/prisma";
import { generateChatTitleWithTimeout } from '@/lib/generate-chat-title';

export async function createMessage(params: {
  userEmail: string;
  content: string;
  currentChatId: string | null;
}) {
  const session = await auth();

  if (!session?.user?.email || session.user.email !== params.userEmail) {
    throw new Error("Unauthorized");
  }

  const [user, aiUser] = await Promise.all([
    prisma.user.findUnique({ where: { email: params.userEmail } }),
    prisma.user.findUnique({ where: { email: '<EMAIL>' } })
  ]);

  if (!user || !aiUser) throw new Error("User configuration error");

  // Check if the chat is shared
  let chat = params.currentChatId
    ? await prisma.chat.findFirst({
      where: {
        id: params.currentChatId,
        OR: [
          { userId: user.id }, // Owner
          { shares: { some: { recipientId: user.id } } } // Shared with user
        ]
      }
    })
    : null;

  if (!chat) {
    // Generate AI title for new chat
    const aiGeneratedTitle = await generateChatTitleWithTimeout(params.content, 3000);

    // Create new chat only if not in a shared context
    chat = await prisma.chat.create({
      data: {
        title: aiGeneratedTitle,
        userId: user.id,
      },
    });
  }

  // Create message in the existing chat (shared or owned)
  await prisma.message.create({
    data: {
      content: params.content,
      type: "USER",
      chatId: chat.id,
      senderId: user.id,
    },
  });

  // Fetch previous messages in the chat for context
  const previousMessages = await prisma.message.findMany({
    where: { chatId: chat.id },
    orderBy: { createdAt: "asc" },
    select: { content: true, type: true }
  });

  // Format messages for Ollama API
  const formattedMessages = previousMessages.map((msg: { type: string; content: any; }) => ({
    role: msg.type === "USER" ? "user" : "assistant",
    content: msg.content
  }));

  // Add the current message with proper type
  formattedMessages.push({
    role: "user",  // Changed to lowercase to match Ollama's expected format
    content: params.content
  });

  // Get AI response with conversation history
  const response = await ollama.chat({
    model: 'gemma3:4b',
    messages: formattedMessages,
  });

  await prisma.message.create({
    data: {
      content: response.message.content,
      type: "ASSISTANT",
      chatId: chat.id,
      senderId: aiUser.id,
    },
  });

  revalidatePath(`/chat/${chat.id}`);
  return { chatId: chat.id, aiResponse: response };
}

export async function getChatMessages(chatId: string) {
  const session = await auth();
  if (!session?.user?.email) {
    throw new Error("Unauthorized");
  }

  // Find the user
  const user = await prisma.user.findUnique({
    where: { email: session.user.email }
  });

  if (!user) {
    throw new Error("User not found");
  }

  // Check if user has access to this chat
  const chatAccess = await prisma.chat.findFirst({
    where: {
      id: chatId,
      OR: [
        { userId: user.id }, // Owner
        { shares: { some: { recipientId: user.id } } } // Shared with user
      ]
    }
  });

  if (!chatAccess) {
    throw new Error("Access denied");
  }

  const messages = await prisma.message.findMany({
    where: { chatId },
    include: {
      sender: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true
        }
      }
    },
    orderBy: { createdAt: "asc" },
  });

  return messages.map((msg) => ({
    content: msg.content,
    type: msg.type,
    sender: msg.sender ? {
      id: msg.sender.id,
      name: msg.sender.name,
      email: msg.sender.email,
      role: msg.sender.role
    } : null,
    createdAt: msg.createdAt
  }));
}

// Delete Chat
export async function deleteChat(chatId: string) {
  const session = await auth();
  if (!session?.user?.email) {
    throw new Error("Unauthorized");
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email }
  });

  if (!user) {
    throw new Error("User not found");
  }

  if (chatId === "all") {
    // Delete all messages and chats for the user
    await prisma.message.deleteMany({
      where: { chat: { userId: user.id } },
    });

    await prisma.chat.deleteMany({
      where: { userId: user.id },
    });
  } else {
    // Check if the chat exists and belongs to the user
    const chat = await prisma.chat.findUnique({
      where: { id: chatId, userId: user.id },
    });

    if (!chat) {
      throw new Error("Chat not found");
    }

    // Delete shares first
    await prisma.share.deleteMany({
      where: { chatId },
    });

    // Delete related messages
    await prisma.message.deleteMany({
      where: { chatId },
    });

    // Now delete the chat
    await prisma.chat.delete({
      where: { id: chatId },
    });
  }

  revalidatePath("/chat"); // Refresh the chat list
}

export async function shareChat(chatId: string, userIds: string[]) {
  const session = await auth();
  if (!session?.user?.email) {
    throw new Error("Unauthorized");
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email }
  });

  if (!user) {
    throw new Error("User not found");
  }

  // Verify chat exists and belongs to sender
  const chat = await prisma.chat.findUnique({
    where: { id: chatId, userId: user.id },
  });

  if (!chat) {
    throw new Error("Chat not found");
  }

  // Find valid recipient users
  const recipients = await prisma.user.findMany({
    where: { id: { in: userIds } },
  });

  // Create share records in transaction
  const result = await prisma.$transaction(
    recipients.map((recipient: { id: any; }) =>
      prisma.share.upsert({
        where: {
          chatId_recipientId: {
            chatId,
            recipientId: recipient.id
          }
        },
        create: {
          chat: {
            connect: { id: chat.id }
          },
          sender: {
            connect: { id: user.id }
          },
          recipient: {
            connect: { id: recipient.id }
          },
        },
        update: {}
      })
    )
  );

  return {
    successCount: result.length,
    invalidIds: userIds.filter(id =>
      !recipients.some((r: { id: string; }) => r.id === id)
    )
  };
}