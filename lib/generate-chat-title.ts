import ollama from 'ollama';

/**
 * Generates a short, meaningful title for a chat based on the user's first message
 * @param userMessage - The user's first message in the chat
 * @returns A promise that resolves to a title (max 25 characters)
 */
export async function generateChatTitle(userMessage: string): Promise<string> {
  try {
    // Fallback title in case AI generation fails
    const fallbackTitle = userMessage.substring(0, 25);
    
    // Don't generate title for very short messages
    if (userMessage.trim().length < 10) {
      return fallbackTitle;
    }

    // Create a prompt for title generation
    const titlePrompt = `Generate a very short title (maximum 25 characters) for a chat conversation that starts with this message: "${userMessage}"

Requirements:
- Maximum 25 characters
- Capture the main topic or intent
- Be concise and clear
- No quotes or special formatting
- Just return the title, nothing else

Title:`;

    // Call Ollama to generate the title
    const response = await ollama.chat({
      model: 'gemma3:4b',
      messages: [
        {
          role: 'user',
          content: titlePrompt
        }
      ],
      options: {
        temperature: 0.3, // Lower temperature for more consistent results
        num_predict: 50,  // Limit response length
      }
    });

    let generatedTitle = response.message.content.trim();
    
    // Clean up the generated title
    generatedTitle = generatedTitle
      .replace(/^["']|["']$/g, '') // Remove quotes
      .replace(/^Title:\s*/i, '')  // Remove "Title:" prefix if present
      .trim();

    // Ensure it's within the character limit
    if (generatedTitle.length > 25) {
      generatedTitle = generatedTitle.substring(0, 25);
    }

    // If the generated title is too short or empty, use fallback
    if (generatedTitle.length < 3) {
      return fallbackTitle;
    }

    return generatedTitle;

  } catch (error) {
    console.error('Error generating chat title:', error);
    // Return fallback title if AI generation fails
    return userMessage.substring(0, 25);
  }
}

/**
 * Generates a chat title with a timeout to prevent hanging
 * @param userMessage - The user's first message in the chat
 * @param timeoutMs - Timeout in milliseconds (default: 5000)
 * @returns A promise that resolves to a title
 */
export async function generateChatTitleWithTimeout(
  userMessage: string, 
  timeoutMs: number = 5000
): Promise<string> {
  const fallbackTitle = userMessage.substring(0, 25);
  
  try {
    const titlePromise = generateChatTitle(userMessage);
    const timeoutPromise = new Promise<string>((resolve) => {
      setTimeout(() => resolve(fallbackTitle), timeoutMs);
    });

    return await Promise.race([titlePromise, timeoutPromise]);
  } catch (error) {
    console.error('Error in generateChatTitleWithTimeout:', error);
    return fallbackTitle;
  }
}
