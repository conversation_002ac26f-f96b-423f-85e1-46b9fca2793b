generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  USER
  ADMIN
}

enum MessageSenderType {
  USER
  ASSISTANT
}

model User {
  id             String    @id @default(cuid())
  name           String?
  email          String    @unique
  role           UserRole  @default(USER)
  image          String?
  password       String?
  chats          Chat[]
  shares         Share[]
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  sentShares     Share[]   @relation("Sender")
  receivedShares Share[]   @relation("Recipient")
  Message        Message[]
}

model Chat {
  id        String    @id @default(cuid())
  title     String?
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  messages  Message[]
  shares    Share[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Message {
  id        String            @id @default(cuid())
  content   String            @db.Text
  type      MessageSenderType
  chatId    String
  chat      Chat              @relation(fields: [chatId], references: [id])
  senderId  String?
  sender    User?             @relation(fields: [senderId], references: [id])
  createdAt DateTime          @default(now())
}

model Share {
  id          String    @id @default(cuid())
  chatId      String
  chat        Chat      @relation(fields: [chatId], references: [id])
  senderId    String
  sender      User      @relation(name: "Sender", fields: [senderId], references: [id])
  recipientId String
  recipient   User      @relation(name: "Recipient", fields: [recipientId], references: [id])
  createdAt   DateTime  @default(now())
  expiresAt   DateTime?
  User        User?     @relation(fields: [userId], references: [id])
  userId      String?

  @@unique([chatId, recipientId])
}
