import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";

import { ModeToggle } from "@/components/ui/themetoggle";
import { Toaster } from "@/components/ui/sonner"
import { ThemeProvider } from "@/components/theme-provider";

export const metadata: Metadata = {
  title: "Chatty",
  description: "IHK Projekt - Maximilian Zenkel",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`antialiased`}
      >
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        {children}
        <ModeToggle />
        <Toaster />
      </ThemeProvider>
      </body>
    </html>
  );
}
