import { NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { message } = await req.json();

    if (!message) {
      return new Response('Message is required', { status: 400 });
    }

    // Create a readable stream for testing
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send initial metadata
          const initialData = {
            type: 'metadata',
            chatId: 'test-chat-id',
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
          );

          // Simulate streaming response
          const testResponse = `This is a test response to your message: "${message}". This response is being streamed word by word to demonstrate the streaming functionality.`;
          const words = testResponse.split(' ');

          for (let i = 0; i < words.length; i++) {
            const word = words[i] + (i < words.length - 1 ? ' ' : '');
            
            // Send streaming chunk
            const chunkData = {
              type: 'chunk',
              content: word,
              timestamp: new Date().toISOString()
            };
            
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`)
            );

            // Add delay to simulate real streaming
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // Send completion signal
          const completionData = {
            type: 'complete',
            fullContent: testResponse,
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(completionData)}\n\n`)
          );

          // Close the stream
          controller.close();
          
        } catch (error) {
          console.error('Test streaming error:', error);
          
          // Send error to client
          const errorData = {
            type: 'error',
            message: 'Test streaming error occurred',
            timestamp: new Date().toISOString()
          };
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`)
          );
          
          controller.close();
        }
      },
    });

    // Return the stream with appropriate headers for Server-Sent Events
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Test API Error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
