
import { auth } from "@/auth";
import { AppSidebar } from "@/components/sidebar/sidebar"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { ChatProvider } from "@/contexts/ChatContext"

export default async function Layout({ children }: { children: React.ReactNode }) {
  const session = await auth();
  return (
    <ChatProvider>
      <SidebarProvider>
        <AppSidebar session={session}/>
        <main className="flex flex-col w-full h-screen">
          <SidebarTrigger className="m-5"/>
          { children }
        </main>
      </SidebarProvider>
    </ChatProvider>
  )
}
