{"name": "chatty", "version": "0.1.0", "private": true, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.9.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.5.0", "lucide-react": "^0.483.0", "next": "14.2.25", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "ollama": "^0.5.14", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.54.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.17.25", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.25", "postcss": "^8", "prisma": "^6.9.0", "tailwindcss": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5.8.2"}}