import NextAuth from "next-auth";
import type { NextAuthConfig } from "next-auth";
import prisma from "./prisma";
import credentials from "next-auth/providers/credentials";
import { LoginSchema } from "./schemas";
import bcrypt from "bcryptjs";

 
export const { handlers, signIn, signOut, auth } = NextAuth({
    session: { strategy: "jwt" },
    callbacks: {
      async jwt({ token, user, trigger, session }) {
        if (user) { // User is available during sign-in
          token.id = user.id
          token.role = user.role
          token.name = user.name
          token.email = user.email
          token.image = user.image
        }

        // Handle session updates (when update() is called)
        if (trigger === "update" && session) {
          // Update token with new session data
          if (session.name !== undefined) token.name = session.name
          if (session.email !== undefined) token.email = session.email
          if (session.image !== undefined) token.image = session.image
        }

        return token
      },
      session({ session, token }) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        session.user.name = token.name as string
        session.user.email = token.email as string
        session.user.image = token.image as string
        return session
      },
    },
    providers: [
      credentials({
        async authorize(credentials) {
          const validatedFields = LoginSchema.safeParse(credentials);
  
          if (validatedFields.success) {
            const { email, password } = validatedFields.data;
  
            const user = await prisma.user.findUnique({ where: { email } });
  
            if (!user || !user.password) return null;
  
            const passwordsMatch = await bcrypt.compare(password, user.password);
            if (passwordsMatch) return user;
          }
  
          return null;
        },
      }),
    ],
})