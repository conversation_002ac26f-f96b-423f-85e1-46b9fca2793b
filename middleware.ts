
import NextAuth from "next-auth";
import { auth } from "./auth";


const authRoutes = ["/login", "/register"];
const publicRoutes = ["/"];

export default auth((req) => {
    const isLoggedIn = !!req.auth;
    const isAuthRoute = authRoutes.includes(req.nextUrl.pathname);
    const isPublicRoute = publicRoutes.includes(req.nextUrl.pathname);
    const isApiAuthRoute = req.nextUrl.pathname.startsWith("/api/auth");


    // Handle authentication routes
    if (isAuthRoute) {
        if (isLoggedIn) {
            return Response.redirect(new URL("/chat", req.nextUrl));
        }
        return;
    }

    // Handle public routes (including landing page)
    if (isPublicRoute) {
        return; // Allow access regardless of authentication status
    }

    // Redirect unauthenticated users to login for other routes
    if (!isLoggedIn) {
        return Response.redirect(new URL("/login", req.nextUrl));
    }
});

export const config = {
    matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};