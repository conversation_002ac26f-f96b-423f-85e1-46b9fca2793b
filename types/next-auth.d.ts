import 'next-auth'
import { UserR<PERSON> } from '@prisma/client'
import { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  interface User {
    role: UserRole
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
  }

  interface AdapterUser extends User {
    role: UserRole
  }

  interface Session {
    user: {
      id: string
      role: UserRole
      name?: string | null
      email?: string | null
      image?: string | null
    } & DefaultSession['user']
  }
}