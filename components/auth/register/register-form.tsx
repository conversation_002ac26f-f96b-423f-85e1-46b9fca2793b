"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { RegisterSchema } from "@/schemas";
import { useState } from "react";

import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";

import Link from "next/link";
import { useTransition } from "react";
import { register } from "@/actions/register-action";

export const RegisterForm = () => {
    const [error, setError] = useState("");
    const [success, setSuccess] = useState("");

    const form = useForm<z.infer<typeof RegisterSchema>>({
        resolver: zodResolver(RegisterSchema),
        defaultValues: {
            email: "",
            password: "",
            name: "",
        },
    });

    const [isPending, startTransition] = useTransition();

    const onSubmit = (values: z.infer<typeof RegisterSchema>) => {
        startTransition(async () => {
          const data = await register(values);
          if (data && data.success) setSuccess(data.success);
          if (data && data.error) setError(data.error);
        });
      };

    return (
        <Card className="w-[400px]">
            <CardHeader>
                <CardTitle>Auth.js</CardTitle>
                <CardDescription>Create an account!</CardDescription>
            </CardHeader>
            <CardContent>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
                        <div className="space-y-4">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Name</FormLabel>
                                        <FormControl>
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Email</FormLabel>
                                        <FormControl>
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="password"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Password</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="******" type="password" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        {
                            success && (
                                <div className="bg-green-500 text-white px-4 py-2 rounded-md">
                                    {success}
                                </div>
                            )
                        }
                        {
                            error && (
                                <div className="bg-red-500  text-white px-4 py-2 rounded-md">{error}</div>
                            )}
                        <Button
                            disabled={isPending}
                            type="submit"
                            size="lg"
                            className="w-full"
                        >
                            Sign up
                        </Button>
                    </form>
                </Form>
            </CardContent>
            <CardFooter className="flex justify-between flex-col">
                <Link className="text-xs" href="/login">
                    Already registered?
                </Link>
            </CardFooter>
        </Card>
    );
};