"use client";

import { <PERSON><PERSON><PERSON>Up, MessageCircle, Plus } from "lucide-react";
import { useSession } from "next-auth/react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import SignOutBtn from "@/components/sidebar/signout-btn";
import { ChatActions } from "@/components/chat/ChatActions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useChatContext } from "@/contexts/ChatContext";
import { useChatPreloader } from "@/hooks/use-chat-preloader";
import { useEffect, useRef } from "react";
import { CombinedChat } from "@/types";
import { useRouter, usePathname } from "next/navigation";

interface User {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  role: "ADMIN" | "USER";
}

interface Session {
  user: User;
}

interface ClientSidebarProps {
  session: Session | null;
  initialChats: CombinedChat[];
}

export function ClientSidebar({ session: initialSession, initialChats }: ClientSidebarProps) {
  const { data: liveSession } = useSession(); // Use live session from NextAuth
  const session = liveSession || initialSession; // Fallback to initial session if live session is not available
  const { state, setChats, setCurrentChat } = useChatContext();

  // Debug session changes
  useEffect(() => {
    if (liveSession) {
      console.log("Sidebar: Live session updated:", liveSession.user);
    }
  }, [liveSession]);
  const { handleChatHover, handleChatHoverEnd, isChatPreloaded } =
    useChatPreloader({
      preloadOnHover: true,
      preloadAdjacent: true,
      preloadRecent: true,
      maxPreloadCount: 5,
      hoverDelay: 300,
    });

  const router = useRouter();
  const pathname = usePathname();
  const initializedRef = useRef(false);

  // Initialize chats on mount
  useEffect(() => {
    if (!initializedRef.current && initialChats.length > 0) {
      setChats(initialChats);
      initializedRef.current = true;
    }
  }, [initialChats, setChats]);

  // Update current chat based on URL
  useEffect(() => {
    const chatIdMatch = pathname.match(/^\/chat\/([^\/]+)$/);
    const currentChatId = chatIdMatch ? chatIdMatch[1] : null;
    setCurrentChat(currentChatId);
  }, [pathname, setCurrentChat]);

  // Handle chat navigation with preloading
  const handleChatClick = (chatId: string, e: React.MouseEvent) => {
    e.preventDefault();

    // Set current chat immediately for instant UI feedback
    setCurrentChat(chatId);

    // Navigate to the chat
    router.push(`/chat/${chatId}`);
  };

  if (!session) {
    return null;
  }

  // Always prioritize context state, fallback to initial chats only if context is empty
  const chats = state.chats.length > 0 ? state.chats : initialChats;

  return (
    <Sidebar collapsible="icon">
      <SidebarContent className="pt-3">
        <SidebarGroup>
          <SidebarGroupLabel>Chats</SidebarGroupLabel>
          {/* <ClearChats /> */}

          <SidebarGroupContent>
            <SidebarMenu>
              {chats.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    asChild
                    className={`
                      transition-all duration-200 hover:bg-sidebar-accent
                      ${
                        state.currentChatId === item.id
                          ? "bg-sidebar-accent"
                          : ""
                      }
                      ${isChatPreloaded(item.id) ? "opacity-100" : "opacity-90"}
                    `}
                  >
                    <div
                      onClick={(e) => handleChatClick(item.id, e)}
                      onMouseEnter={() => handleChatHover(item.id)}
                      onMouseLeave={() => handleChatHoverEnd(item.id)}
                      className="flex items-center gap-2 w-full cursor-pointer"
                    >
                      <MessageCircle
                        className={`
                        transition-colors duration-200
                        ${
                          state.currentChatId === item.id
                            ? "text-sidebar-accent-foreground"
                            : ""
                        }
                      `}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="truncate font-medium">
                          {item.title || "Untitled Chat"}
                        </div>
                        {item.type === "shared" && (
                          <div className="text-xs text-muted-foreground truncate">
                            Shared by{" "}
                            {item.sharedBy.name || item.sharedBy.email}
                          </div>
                        )}
                        {item.type === "owned" &&
                          item.sharedWith.length > 0 && (
                            <div className="text-xs text-muted-foreground truncate">
                              Shared with {item.sharedWith.length} users
                            </div>
                          )}
                      </div>
                      {/* Preload indicator */}
                      {isChatPreloaded(item.id) && (
                        <div className="w-2 h-2 bg-green-500 rounded-full opacity-60" />
                      )}
                    </div>
                  </SidebarMenuButton>

                  {item.type === "owned" && <ChatActions chatId={item.id} />}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  className="bg-white/5 hover:bg-white/10 transition-colors"
                >
                  <Link href="/chat">
                    <Plus />
                    <span>New Chat</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton className="hover:bg-sidebar-accent transition-colors">
                  <Avatar className="w-6 h-6 rounded-full">
                    <AvatarImage src={session?.user?.image ?? undefined} />
                    <AvatarFallback>
                      {session?.user?.name?.substring(0, 2).toUpperCase() ??
                        "U"}
                    </AvatarFallback>
                  </Avatar>
                  <span className="truncate">
                    {session.user.name ?? session.user.email ?? "User"}
                  </span>
                  <ChevronUp className="ml-auto" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="top"
                className="w-[--radix-popper-anchor-width]"
              >
                <DropdownMenuItem asChild>
                  <Link href="/account">Account</Link>
                </DropdownMenuItem>
                {session.user.role === "ADMIN" && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin">Admin Panel</Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem asChild>
                  <SignOutBtn />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
