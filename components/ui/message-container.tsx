"use client";

import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { StreamingMessage } from './streaming-message';
import { Message } from '@/types';

interface MessageContainerProps {
  messages: Message[];
  streamingMessageId?: string | null;
  className?: string;
  autoScroll?: boolean;
  showTimestamps?: boolean;
}

export function MessageContainer({
  messages,
  streamingMessageId,
  className,
  autoScroll = true,
  showTimestamps = false
}: MessageContainerProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, [messages, autoScroll]);

  // Format timestamp
  const formatTimestamp = (date: Date | string) => {
    const messageDate = new Date(date);
    const now = new Date();
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return messageDate.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "flex-1 min-h-0 overflow-y-auto space-y-4 p-4",
        className
      )}
    >
      <div className="w-full max-w-3xl mx-auto space-y-4">
        {messages.map((message, index) => {
          const isStreaming = streamingMessageId === message.id;
          const isLastMessage = index === messages.length - 1;
          
          return (
            <div
              key={message.id}
              className={cn(
                "transform transition-all duration-300 ease-in-out",
                "animate-in slide-in-from-bottom-2 fade-in-0",
                isLastMessage && "animate-in duration-500"
              )}
              style={{
                animationDelay: `${index * 50}ms`,
                animationFillMode: 'both'
              }}
            >
              <StreamingMessage
                content={message.content}
                isStreaming={isStreaming}
                role={message.role}
                sender={message.sender}
                className={cn(
                  "transition-all duration-200",
                  isStreaming && "shadow-lg shadow-blue-500/20"
                )}
              />
              
              {/* Timestamp */}
              {showTimestamps && message.createdAt && (
                <div className={cn(
                  "text-xs text-gray-400 mt-1 px-2",
                  message.role === 'user' ? "text-right" : "text-left"
                )}>
                  {formatTimestamp(message.createdAt)}
                </div>
              )}
            </div>
          );
        })}
        
        {/* Scroll anchor */}
        <div ref={messagesEndRef} className="h-1" />
      </div>
    </div>
  );
}

// Enhanced message list with virtual scrolling for performance
export function VirtualizedMessageContainer({
  messages,
  streamingMessageId,
  className,
  itemHeight = 100,
  overscan = 5
}: MessageContainerProps & {
  itemHeight?: number;
  overscan?: number;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: 10 });

  // Calculate visible items based on scroll position
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollTop = container.scrollTop;
      const containerHeight = container.clientHeight;
      
      const start = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
      const end = Math.min(
        messages.length,
        Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
      );
      
      setVisibleRange({ start, end });
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial calculation

    return () => container.removeEventListener('scroll', handleScroll);
  }, [messages.length, itemHeight, overscan]);

  const visibleMessages = messages.slice(visibleRange.start, visibleRange.end);
  const totalHeight = messages.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;

  return (
    <div
      ref={containerRef}
      className={cn("flex-1 min-h-0 overflow-y-auto", className)}
      style={{ height: '100%' }}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div 
          style={{ 
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleMessages.map((message, index) => {
            const actualIndex = visibleRange.start + index;
            const isStreaming = streamingMessageId === message.id;
            
            return (
              <div
                key={message.id}
                style={{ height: itemHeight }}
                className="flex items-start p-2"
              >
                <StreamingMessage
                  content={message.content}
                  isStreaming={isStreaming}
                  role={message.role}
                  sender={message.sender}
                  className="w-full"
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
